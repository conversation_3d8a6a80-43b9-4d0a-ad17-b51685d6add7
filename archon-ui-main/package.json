{"name": "archon-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "npm run test:coverage:run && npm run test:coverage:summary", "test:coverage:run": "vitest run --coverage --reporter=dot --reporter=json", "test:coverage:stream": "vitest run --coverage --reporter=default --reporter=json --bail=false || true", "test:coverage:summary": "echo '\\n📊 ARCHON TEST & COVERAGE SUMMARY\\n═══════════════════════════════════════\\n' && node -e \"try { const data = JSON.parse(require('fs').readFileSync('coverage/test-results.json', 'utf8')); const passed = data.numPassedTests || 0; const failed = data.numFailedTests || 0; const total = data.numTotalTests || 0; const suites = data.numTotalTestSuites || 0; console.log('Test Suites: ' + (failed > 0 ? '\\x1b[31m' + failed + ' failed\\x1b[0m, ' : '') + '\\x1b[32m' + (suites - failed) + ' passed\\x1b[0m, ' + suites + ' total'); console.log('Tests:       ' + (failed > 0 ? '\\x1b[31m' + failed + ' failed\\x1b[0m, ' : '') + '\\x1b[32m' + passed + ' passed\\x1b[0m, ' + total + ' total'); console.log('\\n✨ Results saved to coverage/test-results.json'); } catch(e) { console.log('⚠️  No test results found. Run tests first!'); }\" || true", "test:coverage:force": "vitest run --coverage --passWithNoTests || true", "seed:projects": "node --loader ts-node/esm ../scripts/seed-project-data.ts"}, "dependencies": {"@milkdown/crepe": "^7.5.0", "@milkdown/kit": "^7.5.0", "@milkdown/plugin-history": "^7.5.0", "@milkdown/preset-commonmark": "^7.5.0", "@xyflow/react": "^12.3.0", "clsx": "latest", "date-fns": "^4.1.0", "fractional-indexing": "^3.2.0", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "prismjs": "^1.30.0", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "socket.io-client": "^4.8.1", "tailwind-merge": "latest", "zod": "^3.25.46"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.19.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "autoprefixer": "latest", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^24.1.0", "postcss": "latest", "tailwindcss": "3.4.17", "ts-node": "^10.9.1", "typescript": "^5.5.4", "vite": "^5.2.0", "vitest": "^1.6.0"}}