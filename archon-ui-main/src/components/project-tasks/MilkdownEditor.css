/* Milkdown Editor Custom Styles - <PERSON>on Theme */

/* Main editor container */
.milkdown-crepe-editor {
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Gradient border effect */
.milkdown-crepe-editor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #a855f7);
  opacity: 0.8;
}

/* Dark mode container */
.dark .milkdown-crepe-editor {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
}

/* Remove default Crepe theme styling */
.milkdown-crepe-editor .milkdown {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Editor content area */
.milkdown-crepe-editor .ProseMirror {
  font-family: Inter, system-ui, -apple-system, sans-serif;
  min-height: 400px;
  max-width: 100%;
  padding: 1rem;
  background: transparent;
  color: #1f2937;
  line-height: 1.6;
}

.dark .milkdown-crepe-editor .ProseMirror {
  color: #f9fafb;
}

/* Remove dark mode filter - use proper theming instead */
.milkdown-theme-dark {
  filter: none;
}

/* Typography */
.milkdown-crepe-editor h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.dark .milkdown-crepe-editor h1 {
  color: #f9fafb;
}

.milkdown-crepe-editor h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #374151;
}

.dark .milkdown-crepe-editor h2 {
  color: #e5e7eb;
}

.milkdown-crepe-editor h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.dark .milkdown-crepe-editor h3 {
  color: #d1d5db;
}

/* Links */
.milkdown-crepe-editor a {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s;
}

.milkdown-crepe-editor a:hover {
  color: #2563eb;
  text-decoration: underline;
}

.dark .milkdown-crepe-editor a {
  color: #60a5fa;
}

.dark .milkdown-crepe-editor a:hover {
  color: #93bbfc;
}

/* Code blocks */
.milkdown-crepe-editor pre {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

.dark .milkdown-crepe-editor pre {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.milkdown-crepe-editor code {
  background: rgba(59, 130, 246, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

.dark .milkdown-crepe-editor code {
  background: rgba(59, 130, 246, 0.2);
}

/* Lists */
.milkdown-crepe-editor ul,
.milkdown-crepe-editor ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.milkdown-crepe-editor li {
  margin: 0.25rem 0;
}

/* Blockquotes */
.milkdown-crepe-editor blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  margin: 1rem 0;
  color: #6b7280;
  font-style: italic;
}

.dark .milkdown-crepe-editor blockquote {
  color: #9ca3af;
  border-left-color: #60a5fa;
}

/* Tables */
.milkdown-crepe-editor table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.milkdown-crepe-editor th,
.milkdown-crepe-editor td {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  text-align: left;
}

.dark .milkdown-crepe-editor th,
.dark .milkdown-crepe-editor td {
  border-color: rgba(255, 255, 255, 0.1);
}

.milkdown-crepe-editor th {
  background: rgba(59, 130, 246, 0.05);
  font-weight: 600;
}

.dark .milkdown-crepe-editor th {
  background: rgba(59, 130, 246, 0.1);
}

/* Toolbar styling */
.milkdown-crepe-editor .milkdown-toolbar {
  background: transparent;
  border: none;
  padding: 0;
  margin-bottom: 1rem;
}

/* Toolbar buttons */
.milkdown-crepe-editor .toolbar-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  margin: 0 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
}

.dark .milkdown-crepe-editor .toolbar-item {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #e5e7eb;
}

.milkdown-crepe-editor .toolbar-item:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

/* Selection */
.milkdown-crepe-editor .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.3);
}

.dark .milkdown-crepe-editor .ProseMirror ::selection {
  background: rgba(96, 165, 250, 0.3);
}

/* Focus state */
.milkdown-crepe-editor .ProseMirror:focus {
  outline: none;
}

/* Placeholder */
.milkdown-crepe-editor .ProseMirror.is-empty::before {
  content: 'Start writing...';
  color: #9ca3af;
  position: absolute;
  pointer-events: none;
}

/* Horizontal rule */
.milkdown-crepe-editor hr {
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
  margin: 2rem 0;
}

/* Save button animation */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.save-button-pulse {
  animation: pulse-glow 2s infinite;
}