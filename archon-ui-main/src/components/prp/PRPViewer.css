/* PRP Viewer Styles - Beautiful Archon Theme */

.prp-viewer {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth collapse animations */
.prp-viewer .collapsible-content {
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s ease-out;
}

/* Hover effects for cards */
.prp-viewer .persona-card,
.prp-viewer .metric-item,
.prp-viewer .flow-diagram {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glow effects for icons */
.prp-viewer .icon-glow {
  filter: drop-shadow(0 0 8px currentColor);
}

/* Gradient text animations */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.prp-viewer .gradient-text {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Section reveal animations */
.prp-viewer .section-content {
  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for important metrics */
@keyframes metricPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.prp-viewer .metric-highlight {
  animation: metricPulse 2s ease-in-out infinite;
}

/* Flow diagram connections */
.prp-viewer .flow-connection {
  position: relative;
}

.prp-viewer .flow-connection::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #3b82f6, #a855f7);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
}

/* Interactive hover states */
.prp-viewer .interactive-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
              0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Dark mode enhancements */
.dark .prp-viewer .interactive-section:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5),
              0 10px 10px -5px rgba(0, 0, 0, 0.2),
              0 0 20px rgba(59, 130, 246, 0.3);
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.prp-viewer .skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Collapsible chevron animation */
.prp-viewer .chevron-animate {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card entrance animations */
.prp-viewer .card-entrance {
  animation: cardSlideUp 0.5s ease-out;
  animation-fill-mode: both;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for lists */
.prp-viewer .stagger-item {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.prp-viewer .stagger-item:nth-child(1) { animation-delay: 0.1s; }
.prp-viewer .stagger-item:nth-child(2) { animation-delay: 0.2s; }
.prp-viewer .stagger-item:nth-child(3) { animation-delay: 0.3s; }
.prp-viewer .stagger-item:nth-child(4) { animation-delay: 0.4s; }
.prp-viewer .stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating animation for icons */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.prp-viewer .float-icon {
  animation: float 3s ease-in-out infinite;
}

/* Glow border effect */
.prp-viewer .glow-border {
  position: relative;
  overflow: hidden;
}

.prp-viewer .glow-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3b82f6, #a855f7, #ec4899, #3b82f6);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  background-size: 400% 400%;
  animation: gradientRotate 3s ease infinite;
}

.prp-viewer .glow-border:hover::before {
  opacity: 1;
}

@keyframes gradientRotate {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Success metric animations */
.prp-viewer .metric-success {
  position: relative;
}

.prp-viewer .metric-success::after {
  content: '✓';
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-weight: bold;
  opacity: 0;
  transition: all 0.3s ease;
}

.prp-viewer .metric-success:hover::after {
  opacity: 1;
  right: 10px;
}

/* Smooth scrolling for sections */
.prp-viewer {
  scroll-behavior: smooth;
}

/* Progress indicator for implementation phases */
.prp-viewer .phase-progress {
  position: relative;
  padding-left: 30px;
}

.prp-viewer .phase-progress::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #3b82f6, #a855f7);
}

.prp-viewer .phase-progress .phase-dot {
  position: absolute;
  left: 6px;
  top: 20px;
  width: 10px;
  height: 10px;
  background: white;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .prp-viewer .grid {
    grid-template-columns: 1fr;
  }
  
  .prp-viewer .text-3xl {
    font-size: 1.5rem;
  }
  
  .prp-viewer .p-6 {
    padding: 1rem;
  }
}