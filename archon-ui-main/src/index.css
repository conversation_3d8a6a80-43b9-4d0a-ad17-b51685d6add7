@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    /* Light mode variables */
    --background: 0 0% 98%;
    --foreground: 240 10% 3.9%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --primary: 271 91% 65%;
    --primary-foreground: 0 0% 100%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --accent: 271 91% 65%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
    --purple-accent: 271 91% 65%;
    --green-accent: 160 84% 39%;
    --pink-accent: 330 90% 65%;
    --blue-accent: 217 91% 60%;
  }
  .dark {
    /* Dark mode variables - keep exactly as they were */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --primary: 271 91% 65%;
    --primary-foreground: 0 0% 100%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --accent: 271 91% 65%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --ring: 240 3.7% 15.9%;
    --radius: 0.5rem;
    --purple-accent: 271 91% 65%;
    --green-accent: 160 84% 39%;
    --pink-accent: 330 90% 65%;
    --blue-accent: 217 91% 60%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
@layer components {
  .neon-grid {
    @apply bg-[linear-gradient(to_right,#a855f720_1px,transparent_1px),linear-gradient(to_bottom,#a855f720_1px,transparent_1px)] bg-[size:40px_40px];
    @apply dark:bg-[linear-gradient(to_right,#a855f730_1px,transparent_1px),linear-gradient(to_bottom,#a855f730_1px,transparent_1px)];
  }
  .neon-divider-h {
    @apply h-[1px] w-full;
  }
  .neon-divider-h.purple {
    @apply bg-purple-500;
  }
  .neon-divider-h.green {
    @apply bg-emerald-500;
  }
  .neon-divider-h.pink {
    @apply bg-pink-500;
  }
  .neon-divider-h.blue {
    @apply bg-blue-500;
  }
  .neon-divider-v {
    @apply w-[1px] h-full;
  }
  .neon-divider-v.purple {
    @apply bg-purple-500;
  }
  .neon-divider-v.green {
    @apply bg-emerald-500;
  }
  .neon-divider-v.pink {
    @apply bg-pink-500;
  }
  .neon-divider-v.blue {
    @apply bg-blue-500;
  }
  .knowledge-item-card {
    @apply relative backdrop-blur-md bg-gradient-to-b from-white/10 to-black/30 border border-purple-500/30 rounded-md p-4 transition-all duration-300;
    @apply before:content-[""] before:absolute before:top-0 before:left-0 before:w-full before:h-[2px] before:bg-purple-500 before:shadow-[0_0_20px_5px_rgba(168,85,247,0.7)];
    @apply after:content-[""] after:absolute after:top-0 after:left-0 after:right-0 after:h-16 after:bg-gradient-to-b after:from-purple-500/20 after:to-purple-500/5 after:rounded-t-md after:pointer-events-none;
    @apply shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
  }
  .knowledge-item-card:hover {
    @apply border-purple-500/70 shadow-[0_15px_40px_-15px_rgba(0,0,0,0.9)] before:shadow-[0_0_25px_8px_rgba(168,85,247,0.8)];
    @apply translate-y-[-2px];
  }
  /* Glassmorphism utility classes */
  .glass {
    /* Light mode (base) styles */
    @apply backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 border border-gray-200 shadow-[0_10px_30px_-15px_rgba(0,0,0,0.1)];
    /* Dark mode overrides */
    @apply dark:bg-gradient-to-b dark:from-white/10 dark:to-black/30 dark:border-zinc-800/50 dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
  }
  .glass-purple {
    /* Light mode (base) styles */
    @apply backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 border border-purple-300 shadow-[0_10px_30px_-15px_rgba(168,85,247,0.15)];
    @apply before:content-[""] before:absolute before:top-0 before:left-0 before:w-full before:h-[2px] before:bg-purple-500 before:shadow-[0_0_10px_2px_rgba(168,85,247,0.4)];
    @apply after:content-[""] after:absolute after:top-0 after:left-0 after:right-0 after:h-16 after:bg-gradient-to-b after:from-purple-100 after:to-white after:rounded-t-md after:pointer-events-none;
    /* Dark mode overrides */
    @apply dark:from-white/10 dark:to-black/30 dark:border-purple-500/30 dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
    @apply dark:before:shadow-[0_0_20px_5px_rgba(168,85,247,0.7)];
    @apply dark:after:from-purple-500/20 dark:after:to-purple-500/5;
  }
  .glass-green {
    /* Light mode (base) styles */
    @apply backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 border border-emerald-300 shadow-[0_10px_30px_-15px_rgba(16,185,129,0.15)];
    @apply before:content-[""] before:absolute before:top-0 before:left-0 before:w-full before:h-[2px] before:bg-emerald-500 before:shadow-[0_0_10px_2px_rgba(16,185,129,0.4)];
    @apply after:content-[""] after:absolute after:top-0 after:left-0 after:right-0 after:h-16 after:bg-gradient-to-b after:from-emerald-100 after:to-white after:rounded-t-md after:pointer-events-none;
    /* Dark mode overrides */
    @apply dark:from-white/10 dark:to-black/30 dark:border-emerald-500/30 dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
    @apply dark:before:shadow-[0_0_20px_5px_rgba(16,185,129,0.7)];
    @apply dark:after:from-emerald-500/20 dark:after:to-emerald-500/5;
  }
  .glass-pink {
    /* Light mode (base) styles */
    @apply backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 border border-pink-300 shadow-[0_10px_30px_-15px_rgba(236,72,153,0.15)];
    @apply before:content-[""] before:absolute before:top-0 before:left-0 before:w-full before:h-[2px] before:bg-pink-500 before:shadow-[0_0_10px_2px_rgba(236,72,153,0.4)];
    @apply after:content-[""] after:absolute after:top-0 after:left-0 after:right-0 after:h-16 after:bg-gradient-to-b after:from-pink-100 after:to-white after:rounded-t-md after:pointer-events-none;
    /* Dark mode overrides */
    @apply dark:from-white/10 dark:to-black/30 dark:border-pink-500/30 dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
    @apply dark:before:shadow-[0_0_20px_5px_rgba(236,72,153,0.7)];
    @apply dark:after:from-pink-500/20 dark:after:to-pink-500/5;
  }
  .glass-blue {
    /* Light mode (base) styles */
    @apply backdrop-blur-md bg-gradient-to-b from-white/80 to-white/60 border border-blue-300 shadow-[0_10px_30px_-15px_rgba(59,130,246,0.15)];
    @apply before:content-[""] before:absolute before:top-0 before:left-0 before:w-full before:h-[2px] before:bg-blue-500 before:shadow-[0_0_10px_2px_rgba(59,130,246,0.4)];
    @apply after:content-[""] after:absolute after:top-0 after:left-0 after:right-0 after:h-16 after:bg-gradient-to-b after:from-blue-100 after:to-white after:rounded-t-md after:pointer-events-none;
    /* Dark mode overrides */
    @apply dark:from-white/10 dark:to-black/30 dark:border-blue-500/30 dark:shadow-[0_10px_30px_-15px_rgba(0,0,0,0.7)];
    @apply dark:before:shadow-[0_0_20px_5px_rgba(59,130,246,0.7)];
    @apply dark:after:from-blue-500/20 dark:after:to-blue-500/5;
  }
  /* Hide scrollbar but allow scrolling */
  .hide-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
  /* Card flip animations */
  .flip-card .backface-hidden {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
  .rotate-y-180 {
    transform: rotateY(180deg);
  }
  .transform-style-preserve-3d {
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
  }
}
/* Animation delays */
.animation-delay-150 {
  animation-delay: 150ms;
}
.animation-delay-300 {
  animation-delay: 300ms;
}

/* Card expansion animation */
.card-collapsed {
  height: 140px;
  transition: height 0.5s ease-in-out;
}

.card-expanded {
  height: 280px;
  transition: height 0.5s ease-in-out;
}

/* Ensure scrollable content in expanded cards */
.card-expanded .flex-1.overflow-hidden > .absolute {
  /* Removed max-height to allow full scrolling */
}

/* Screensaver Animations */
@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  33% {
    transform: translateY(-30px) translateX(10px);
  }
  66% {
    transform: translateY(30px) translateX(-10px);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes hologram {
  0%, 100% {
    opacity: 1;
    transform: rotateY(0deg) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: rotateY(10deg) scale(1.02);
  }
}

@keyframes scan {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes etherealFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.05);
    opacity: 0.8;
  }
}

@keyframes glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px 10px rgba(59, 130, 246, 0.5),
                0 0 40px 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px 15px rgba(59, 130, 246, 0.7),
                0 0 60px 30px rgba(59, 130, 246, 0.4);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(59, 130, 246, 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.4);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(59, 130, 246, 0.6);
}

/* Thin scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}