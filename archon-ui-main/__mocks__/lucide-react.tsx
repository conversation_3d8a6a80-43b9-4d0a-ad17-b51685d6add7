import React from 'react'
import { vi } from 'vitest'

const createMockIcon = (name: string) => {
  const MockIcon = React.forwardRef(({ className, ...props }: any, ref: any) => (
    <span
      ref={ref}
      className={className}
      data-testid={`${name.toLowerCase()}-icon`}
      data-lucide={name}
      {...props}
    >
      {name}
    </span>
  ))
  MockIcon.displayName = name
  return MockIcon
}

// Export all icons used in the app
export const Settings = createMockIcon('Settings')
export const Check = createMockIcon('Check')
export const CheckCircle = createMockIcon('CheckCircle')
export const X = createMockIcon('X')
export const XCircle = createMockIcon('XCircle')
export const Eye = createMockIcon('Eye')
export const EyeOff = createMockIcon('EyeOff')
export const Save = createMockIcon('Save')
export const Loader = createMockIcon('Loader')
export const Loader2 = createMockIcon('Loader2')
export const RefreshCw = createMockIcon('RefreshCw')
export const Play = createMockIcon('Play')
export const Pause = createMockIcon('Pause')
export const Square = createMockIcon('Square')
export const FileText = createMockIcon('FileText')
export const Download = createMockIcon('Download')
export const Upload = createMockIcon('Upload')
export const ChevronDown = createMockIcon('ChevronDown')
export const ChevronUp = createMockIcon('ChevronUp')
export const ChevronLeft = createMockIcon('ChevronLeft')
export const ChevronRight = createMockIcon('ChevronRight')
export const Plus = createMockIcon('Plus')
export const Minus = createMockIcon('Minus')
export const Edit = createMockIcon('Edit')
export const Edit2 = createMockIcon('Edit2')
export const Edit3 = createMockIcon('Edit3')
export const Trash = createMockIcon('Trash')
export const Trash2 = createMockIcon('Trash2')
export const User = createMockIcon('User')
export const Users = createMockIcon('Users')
export const Bot = createMockIcon('Bot')
export const Database = createMockIcon('Database')
export const Server = createMockIcon('Server')
export const Globe = createMockIcon('Globe')
export const Search = createMockIcon('Search')
export const Filter = createMockIcon('Filter')
export const Copy = createMockIcon('Copy')
export const ExternalLink = createMockIcon('ExternalLink')
export const Info = createMockIcon('Info')
export const AlertCircle = createMockIcon('AlertCircle')
export const AlertTriangle = createMockIcon('AlertTriangle')
export const Zap = createMockIcon('Zap')
export const Code = createMockIcon('Code')
export const Terminal = createMockIcon('Terminal')
export const Book = createMockIcon('Book')
export const BookOpen = createMockIcon('BookOpen')
export const Folder = createMockIcon('Folder')
export const FolderOpen = createMockIcon('FolderOpen')
export const File = createMockIcon('File')
export const Hash = createMockIcon('Hash')
export const Tag = createMockIcon('Tag')
export const Clock = createMockIcon('Clock')
export const Calendar = createMockIcon('Calendar')
export const MapPin = createMockIcon('MapPin')
export const Link = createMockIcon('Link')
export const Mail = createMockIcon('Mail')
export const Phone = createMockIcon('Phone')
export const Home = createMockIcon('Home')
export const Menu = createMockIcon('Menu')
export const MoreHorizontal = createMockIcon('MoreHorizontal')
export const MoreVertical = createMockIcon('MoreVertical')
export const Refresh = createMockIcon('Refresh')
export const RotateCcw = createMockIcon('RotateCcw')
export const RotateCw = createMockIcon('RotateCw')
export const Sun = createMockIcon('Sun')
export const Moon = createMockIcon('Moon')
export const Monitor = createMockIcon('Monitor')
export const Wifi = createMockIcon('Wifi')
export const WifiOff = createMockIcon('WifiOff')
export const Volume2 = createMockIcon('Volume2')
export const VolumeX = createMockIcon('VolumeX')
export const BarChart = createMockIcon('BarChart')
export const PieChart = createMockIcon('PieChart')
export const TrendingUp = createMockIcon('TrendingUp')
export const TrendingDown = createMockIcon('TrendingDown')
export const ArrowUp = createMockIcon('ArrowUp')
export const ArrowDown = createMockIcon('ArrowDown')
export const ArrowLeft = createMockIcon('ArrowLeft')
export const ArrowRight = createMockIcon('ArrowRight')
export const Send = createMockIcon('Send')
export const MessageSquare = createMockIcon('MessageSquare')
export const MessageCircle = createMockIcon('MessageCircle')
export const Heart = createMockIcon('Heart')
export const Star = createMockIcon('Star')
export const Bookmark = createMockIcon('Bookmark')
export const Share = createMockIcon('Share')
export const Share2 = createMockIcon('Share2')
export const Maximize = createMockIcon('Maximize')
export const Minimize = createMockIcon('Minimize')
export const Expand = createMockIcon('Expand')
export const Shrink = createMockIcon('Shrink')
export const Move = createMockIcon('Move')
export const Shuffle = createMockIcon('Shuffle')
export const Repeat = createMockIcon('Repeat')
export const StopCircle = createMockIcon('StopCircle')
export const SkipBack = createMockIcon('SkipBack')
export const SkipForward = createMockIcon('SkipForward')
export const FastForward = createMockIcon('FastForward')
export const Rewind = createMockIcon('Rewind')
export const Camera = createMockIcon('Camera')
export const Image = createMockIcon('Image')
export const Video = createMockIcon('Video')
export const Mic = createMockIcon('Mic')
export const MicOff = createMockIcon('MicOff')
export const Headphones = createMockIcon('Headphones')
export const Speaker = createMockIcon('Speaker')
export const Bell = createMockIcon('Bell')
export const BellOff = createMockIcon('BellOff')
export const Shield = createMockIcon('Shield')
export const ShieldCheck = createMockIcon('ShieldCheck')
export const ShieldAlert = createMockIcon('ShieldAlert')
export const Key = createMockIcon('Key')
export const Lock = createMockIcon('Lock')
export const Unlock = createMockIcon('Unlock')
export const LogIn = createMockIcon('LogIn')
export const LogOut = createMockIcon('LogOut')
export const UserPlus = createMockIcon('UserPlus')
export const UserMinus = createMockIcon('UserMinus')
export const UserCheck = createMockIcon('UserCheck')
export const UserX = createMockIcon('UserX')
export const Package = createMockIcon('Package')
export const Package2 = createMockIcon('Package2')
export const ShoppingCart = createMockIcon('ShoppingCart')
export const ShoppingBag = createMockIcon('ShoppingBag')
export const CreditCard = createMockIcon('CreditCard')
export const DollarSign = createMockIcon('DollarSign')
export const Percent = createMockIcon('Percent')
export const Activity = createMockIcon('Activity')
export const Cpu = createMockIcon('Cpu')
export const HardDrive = createMockIcon('HardDrive')
export const MemoryStick = createMockIcon('MemoryStick')
export const Smartphone = createMockIcon('Smartphone')
export const Tablet = createMockIcon('Tablet')
export const Laptop = createMockIcon('Laptop')
export const Monitor2 = createMockIcon('Monitor2')
export const Tv = createMockIcon('Tv')
export const Watch = createMockIcon('Watch')
export const Gamepad2 = createMockIcon('Gamepad2')
export const Mouse = createMockIcon('Mouse')
export const Keyboard = createMockIcon('Keyboard')
export const Printer = createMockIcon('Printer')
export const Scanner = createMockIcon('Scanner')
export const Webcam = createMockIcon('Webcam')
export const Bluetooth = createMockIcon('Bluetooth')
export const Usb = createMockIcon('Usb')
export const Zap2 = createMockIcon('Zap2')
export const Battery = createMockIcon('Battery')
export const BatteryCharging = createMockIcon('BatteryCharging')
export const Plug = createMockIcon('Plug')
export const Power = createMockIcon('Power')
export const PowerOff = createMockIcon('PowerOff')
export const BarChart2 = createMockIcon('BarChart2')
export const BarChart3 = createMockIcon('BarChart3')
export const BarChart4 = createMockIcon('BarChart4')
export const LineChart = createMockIcon('LineChart')
export const PieChart2 = createMockIcon('PieChart2')
export const Layers = createMockIcon('Layers')
export const Layers2 = createMockIcon('Layers2')
export const Layers3 = createMockIcon('Layers3')
export const Grid = createMockIcon('Grid')
export const Grid2x2 = createMockIcon('Grid2x2')
export const Grid3x3 = createMockIcon('Grid3x3')
export const List = createMockIcon('List')
export const ListChecks = createMockIcon('ListChecks')
export const ListTodo = createMockIcon('ListTodo')
export const CheckSquare = createMockIcon('CheckSquare')
export const Square2 = createMockIcon('Square2')
export const Circle = createMockIcon('Circle')
export const CircleCheck = createMockIcon('CircleCheck')
export const CircleX = createMockIcon('CircleX')
export const CircleDot = createMockIcon('CircleDot')
export const Target = createMockIcon('Target')
export const Focus = createMockIcon('Focus')
export const Crosshair = createMockIcon('Crosshair')
export const Locate = createMockIcon('Locate')
export const LocateFixed = createMockIcon('LocateFixed')
export const Navigation = createMockIcon('Navigation')
export const Navigation2 = createMockIcon('Navigation2')
export const Compass = createMockIcon('Compass')
export const Map = createMockIcon('Map')
export const TestTube = createMockIcon('TestTube')
export const FlaskConical = createMockIcon('FlaskConical')
export const Bug = createMockIcon('Bug')
export const GitBranch = createMockIcon('GitBranch')
export const GitCommit = createMockIcon('GitCommit')
export const GitMerge = createMockIcon('GitMerge')
export const GitPullRequest = createMockIcon('GitPullRequest')
export const Github = createMockIcon('Github')
export const Gitlab = createMockIcon('Gitlab')
export const Bitbucket = createMockIcon('Bitbucket')
export const Network = createMockIcon('Network')
export const GitGraph = createMockIcon('GitGraph')
export const ListFilter = createMockIcon('ListFilter')
export const CheckSquare2 = createMockIcon('CheckSquare2')
export const CircleSlash2 = createMockIcon('CircleSlash2')
export const Clock3 = createMockIcon('Clock3')
export const GitCommitHorizontal = createMockIcon('GitCommitHorizontal')
export const CalendarDays = createMockIcon('CalendarDays')
export const Sparkles = createMockIcon('Sparkles')
export const Layout = createMockIcon('Layout')
export const Table = createMockIcon('Table')
export const Columns = createMockIcon('Columns')
export const GitPullRequestDraft = createMockIcon('GitPullRequestDraft')
export const BrainCircuit = createMockIcon('BrainCircuit')
export const Wrench = createMockIcon('Wrench')
export const PlugZap = createMockIcon('PlugZap')
export const BoxIcon = createMockIcon('BoxIcon')
export const Box = createMockIcon('Box')
export const Brain = createMockIcon('Brain')
export const LinkIcon = createMockIcon('LinkIcon')
export const Sparkle = createMockIcon('Sparkle')
export const FolderTree = createMockIcon('FolderTree')
export const Lightbulb = createMockIcon('Lightbulb')
export const Rocket = createMockIcon('Rocket')
export const Building = createMockIcon('Building')
export const FileCode = createMockIcon('FileCode')
export const FileJson = createMockIcon('FileJson')
export const Braces = createMockIcon('Braces')
export const Binary = createMockIcon('Binary')
export const Palette = createMockIcon('Palette')
export const Paintbrush = createMockIcon('Paintbrush')
export const Type = createMockIcon('Type')
export const Heading = createMockIcon('Heading')
export const AlignLeft = createMockIcon('AlignLeft')
export const AlignCenter = createMockIcon('AlignCenter')
export const AlignRight = createMockIcon('AlignRight')
export const Bold = createMockIcon('Bold')
export const Italic = createMockIcon('Italic')
export const Underline = createMockIcon('Underline')
export const Strikethrough = createMockIcon('Strikethrough')
export const FileCheck = createMockIcon('FileCheck')
export const FileX = createMockIcon('FileX')
export const FilePlus = createMockIcon('FilePlus')
export const FileMinus = createMockIcon('FileMinus')
export const FolderPlus = createMockIcon('FolderPlus')
export const FolderMinus = createMockIcon('FolderMinus')
export const FolderCheck = createMockIcon('FolderCheck')
export const FolderX = createMockIcon('FolderX')
export const startMCPServer = createMockIcon('startMCPServer')
export const Pin = createMockIcon('Pin')
export const CheckCircle2 = createMockIcon('CheckCircle2')
export const Clipboard = createMockIcon('Clipboard')
export const LayoutGrid = createMockIcon('LayoutGrid')
export const Pencil = createMockIcon('Pencil')
export const MousePointer = createMockIcon('MousePointer')
export const GripVertical = createMockIcon('GripVertical')
export const History = createMockIcon('History')
export const PlusCircle = createMockIcon('PlusCircle')
export const MinusCircle = createMockIcon('MinusCircle')
export const ChevronDownIcon = createMockIcon('ChevronDownIcon')
export const FileIcon = createMockIcon('FileIcon')
export const AlertCircleIcon = createMockIcon('AlertCircleIcon')
export const Clock4 = createMockIcon('Clock4')
export const XIcon = createMockIcon('XIcon')
export const CheckIcon = createMockIcon('CheckIcon')
export const TrashIcon = createMockIcon('TrashIcon')
export const EyeIcon = createMockIcon('EyeIcon')
export const EditIcon = createMockIcon('EditIcon')
export const DownloadIcon = createMockIcon('DownloadIcon')
export const RefreshIcon = createMockIcon('RefreshIcon')
export const SearchIcon = createMockIcon('SearchIcon')
export const FilterIcon = createMockIcon('FilterIcon')
export const PlusIcon = createMockIcon('PlusIcon')
export const FolderIcon = createMockIcon('FolderIcon')
export const FileTextIcon = createMockIcon('FileTextIcon')
export const BookOpenIcon = createMockIcon('BookOpenIcon')
export const DatabaseIcon = createMockIcon('DatabaseIcon')
export const GlobeIcon = createMockIcon('GlobeIcon')
export const TagIcon = createMockIcon('TagIcon')
export const CalendarIcon = createMockIcon('CalendarIcon')
export const ClockIcon = createMockIcon('ClockIcon')
export const UserIcon = createMockIcon('UserIcon')
export const SettingsIcon = createMockIcon('SettingsIcon')
export const InfoIcon = createMockIcon('InfoIcon')
export const WarningIcon = createMockIcon('WarningIcon')
export const ErrorIcon = createMockIcon('ErrorIcon')