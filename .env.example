# Minimal startup configuration - only Supabase connection required
# All other settings (API keys, model choices, RAG flags) are managed via the Settings page

# Get your SUPABASE_URL from the Data API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API Keys section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api-keys
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Optional: Set log level for debugging
LOGFIRE_TOKEN=
LOG_LEVEL=INFO

# Service Ports Configuration
# These ports are used for external access to the services
HOST=localhost
ARCHON_SERVER_PORT=8181
ARCHON_MCP_PORT=8051
ARCHON_AGENTS_PORT=8052
ARCHON_UI_PORT=3737
ARCHON_DOCS_PORT=3838

# Embedding Configuration
# Dimensions for embedding vectors (1536 for OpenAI text-embedding-3-small)
EMBEDDING_DIMENSIONS=1536

# NOTE: All other configuration has been moved to database management!
# Run the credentials_setup.sql file in your Supabase SQL editor to set up the credentials table.
# Then use the Settings page in the web UI to manage:
# - OPENAI_API_KEY (encrypted)
# - MODEL_CHOICE 
# - TRANSPORT settings
# - RAG strategy flags (USE_CONTEXTUAL_EMBEDDINGS, USE_HYBRID_SEARCH, etc.)