name: 🐛 Bug Report
description: Report a bug to help us improve Archon V2 Alpha
title: "🐛 [Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        # 🐛 Bug Report for Archon V2 Alpha
        
        Thank you for taking the time to report a bug! This helps us improve Archon for everyone.

  - type: input
    id: archon-version
    attributes:
      label: Archon Version
      description: What version of Archon are you running?
      placeholder: "v0.1.0 or check package.json"
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: Bug Severity
      description: How severe is this bug?
      options:
        - "🟢 Low - Minor inconvenience"
        - "🟡 Medium - Affects functionality" 
        - "🟠 High - Blocks important features"
        - "🔴 Critical - App unusable"
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: What were you trying to do when this bug occurred?
      placeholder: "I was trying to crawl a documentation site when..."
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: Detailed steps to reproduce the bug
      placeholder: |
        1. Go to Knowledge Base page
        2. Click "Add Knowledge"
        3. Enter URL: https://example.com
        4. Click "Add Source"
        5. Error occurs...
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: What should have happened?
      placeholder: "The site should have been crawled successfully and added to my knowledge base..."
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual Behavior
      description: What actually happened?
      placeholder: "Instead, I got an error message and the crawling failed..."
    validations:
      required: true

  - type: textarea
    id: error-details
    attributes:
      label: Error Details (if any)
      description: Copy and paste any error messages, stack traces, or console errors
      placeholder: |
        Error: Failed to crawl URL
        at CrawlingService.crawlUrl (/app/src/services/crawling.js:123:15)
        at async POST /api/knowledge/crawl
      render: text

  - type: dropdown
    id: component
    attributes:
      label: Affected Component
      description: Which part of Archon is affected?
      options:
        - "🔍 Knowledge Base / RAG"
        - "🔗 MCP Integration"
        - "📋 Projects & Tasks (if enabled)" 
        - "⚙️ Settings & Configuration"
        - "🖥️ User Interface"
        - "🐳 Docker / Infrastructure"
        - "❓ Not Sure"
    validations:
      required: true

  - type: input
    id: browser-os
    attributes:
      label: Browser & OS
      description: What browser and operating system are you using?
      placeholder: "Chrome 122 on macOS 14.1"
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other context about the problem (screenshots, logs, etc.)
      placeholder: "Add any other context here..."

  - type: checkboxes
    id: service-status
    attributes:
      label: Service Status (check all that are working)
      description: Which Archon services were running when the bug occurred?
      options:
        - label: "🖥️ Frontend UI (http://localhost:3737)"
        - label: "⚙️ Main Server (http://localhost:8181)"
        - label: "🔗 MCP Service (localhost:8051)" 
        - label: "🤖 Agents Service (http://localhost:8052)"
        - label: "💾 Supabase Database (connected)"